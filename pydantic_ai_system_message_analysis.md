# Pydantic-AI System Message 生成分析

## 概述

你看到的复杂system message是pydantic-ai自动生成的，它将用户的简单配置转换为包含工具调用指令的完整prompt。

## System Message 结构分析

### 原始System Message分解

```
<|im_start|>system
please extract the user profile information from the following text. The output should be a JSON object with the keys "name", "dob" (date of birth), and "bio" (a short biography). If any information is not available, leave that key out of the JSON object.

# Tools

You may call one or more functions to assist with the user query.

You are provided with function signatures within XML tags:

{"type": "function", "function": {"name": "final_result", "description": "The final response which ends this conversation", "parameters": {"additionalProperties": false, "properties": {"name": {"type": "string"}, "dob": {"format": "date", "type": "string"}, "bio": {"type": "string"}}, "type": "object"}}}


For each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:
<tool_call>
{"name": , "arguments": }
</tool_call>
<|im_end|>
```

### 组成部分

1. **用户原始system_prompt**:
   ```
   please extract the user profile information from the following text. The output should be a JSON object with the keys "name", "dob" (date of birth), and "bio" (a short biography). If any information is not available, leave that key out of the JSON object.
   ```

2. **工具调用说明**:
   ```
   # Tools
   
   You may call one or more functions to assist with the user query.
   ```

3. **函数签名定义**:
   ```json
   {"type": "function", "function": {"name": "final_result", "description": "The final response which ends this conversation", "parameters": {"additionalProperties": false, "properties": {"name": {"type": "string"}, "dob": {"format": "date", "type": "string"}, "bio": {"type": "string"}}, "type": "object"}}}
   ```

4. **调用格式说明**:
   ```
   For each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:
   <tool_call>
   {"name": , "arguments": }
   </tool_call>
   ```

## Pydantic-AI 如何生成这个Message

### 1. 用户代码
```python
class UserProfile(TypedDict, total=False):
    name: str
    dob: date
    bio: str

self.agent = Agent(
    model=self.model,
    system_prompt='please extract the user profile information...',
    output_type=UserProfile  # 这里触发了工具调用生成
)
```

### 2. Pydantic-AI 内部处理流程

1. **检测到output_type**: pydantic-ai发现用户指定了`output_type=UserProfile`
2. **生成工具定义**: 自动将`UserProfile`转换为OpenAI function calling格式
3. **构建system prompt**: 将用户的system_prompt与工具调用指令合并
4. **添加格式说明**: 添加XML标签格式的调用说明

### 3. 生成的工具定义

从`UserProfile`类型定义：
```python
class UserProfile(TypedDict, total=False):
    name: str
    dob: date
    bio: str
```

自动生成为：
```json
{
  "type": "function",
  "function": {
    "name": "final_result",
    "description": "The final response which ends this conversation",
    "parameters": {
      "additionalProperties": false,
      "properties": {
        "name": {"type": "string"},
        "dob": {"format": "date", "type": "string"},
        "bio": {"type": "string"}
      },
      "type": "object"
    }
  }
}
```

## 为什么会导致vLLM冻结

这个复杂的system message配合guided_json参数，创建了一个非常复杂的JSON schema：

1. **工具调用schema**: 要求输出必须是工具调用格式
2. **嵌套结构**: `anyOf` + `enum` + `additionalProperties: false`
3. **复杂约束**: 多层嵌套的对象定义

## 如何手动构建相同的请求

如果你想手动发送相同的请求，需要：

1. **完整的system message** (包含工具调用指令)
2. **对应的guided_json schema** (用于约束输出格式)
3. **正确的消息格式** (ChatML格式)

## 简化版本

如果你想避免这个问题，可以使用简化的配置：

```python
# 不使用output_type，避免复杂的工具调用
self.agent = Agent(
    model=self.model,
    system_prompt='Extract user profile as JSON with name, dob, bio fields.',
    # 不指定output_type，让模型自由输出
)
```

这样pydantic-ai就不会生成复杂的工具调用system message，也不会触发vLLM的冻结问题。
