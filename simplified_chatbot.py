from datetime import date
from typing import Dict, List, Optional
from loguru import logger
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic import ValidationError, BaseModel
from typing_extensions import TypedDict

# 使用更简单的数据结构，避免复杂的嵌套
class UserProfile(BaseModel):
    name: Optional[str] = None
    dob: Optional[date] = None
    bio: Optional[str] = None

class Chatbot:
    def __init__(self):
        self.model = OpenAIModel(
            model_name="qwen2.5-14b-instruct",
            provider=OpenAIProvider(
                base_url="http://0.0.0.0:7509/v1/",
                api_key="password"
            ),
        )
        # 使用简单的系统提示，避免复杂的工具调用
        self.agent = Agent(
            model=self.model,
            system_prompt='Extract user profile information and return a JSON object with keys "name", "dob" (date of birth), and "bio" (biography). If information is missing, omit that key.',
            result_type=UserProfile
        )

async def main():
    chatbot = Chatbot()
    user_input = 'My name is <PERSON>, I was born on January 28th 1990, I like the chain the dog and the pyramid.'

    try:
        # 使用简单的运行方式，避免复杂的流式处理
        result = await chatbot.agent.run(user_input)
        print("Result:", result.data)
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
