#!/usr/bin/env python3
"""
最小化复现vLLM冻结问题的脚本
专门针对pydantic-ai生成的problematic JSON schema
"""

import requests
import json
import time

# 从日志中提取的导致冻结的确切JSON schema
PROBLEMATIC_SCHEMA = {
    "type": "array",
    "minItems": 1,
    "items": {
        "type": "object",
        "anyOf": [
            {
                "properties": {
                    "name": {
                        "type": "string",
                        "enum": ["final_result"]
                    },
                    "parameters": {
                        "additionalProperties": False,
                        "properties": {
                            "name": {"type": "string"},
                            "dob": {"format": "date", "type": "string"},
                            "bio": {"type": "string"}
                        },
                        "type": "object"
                    }
                },
                "required": ["name", "parameters"]
            }
        ]
    }
}

def test_freeze_with_curl_command():
    """生成可以直接使用的curl命令"""
    
    payload = {
        "model": "qwen2.5-14b-instruct",
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "guided_json": PROBLEMATIC_SCHEMA,
        "max_tokens": 100
    }
    
    curl_cmd = f"""curl -X POST http://0.0.0.0:7509/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -d '{json.dumps(payload, separators=(",", ":"))}' \\
  --max-time 60"""
    
    print("🔧 使用以下curl命令复现问题:")
    print("=" * 50)
    print(curl_cmd)
    print("=" * 50)

def test_with_python(url="http://0.0.0.0:7509"):
    """使用Python直接测试"""
    
    payload = {
        "model": "qwen2.5-14b-instruct", 
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "guided_json": PROBLEMATIC_SCHEMA,
        "max_tokens": 100
    }
    
    print(f"🚀 发送请求到: {url}/v1/chat/completions")
    print(f"📊 Schema复杂度: {len(json.dumps(PROBLEMATIC_SCHEMA))} 字符")
    
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{url}/v1/chat/completions",
            json=payload,
            timeout=30
        )
        
        elapsed = time.time() - start_time
        print(f"⏱️  响应时间: {elapsed:.2f}秒")
        
        if response.status_code == 200:
            print("✅ 请求成功")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("🚨 请求超时 - 可能表明服务器冻结!")
    except Exception as e:
        print(f"💥 错误: {e}")

def analyze_schema():
    """分析导致问题的schema结构"""
    
    print("🔍 分析问题Schema:")
    print("=" * 40)
    print(json.dumps(PROBLEMATIC_SCHEMA, indent=2))
    print("=" * 40)
    
    print("\n📋 Schema特征:")
    print(f"  • 类型: {PROBLEMATIC_SCHEMA['type']}")
    print(f"  • 最小项目数: {PROBLEMATIC_SCHEMA['minItems']}")
    print(f"  • 包含anyOf结构: {'anyOf' in str(PROBLEMATIC_SCHEMA)}")
    print(f"  • 包含enum约束: {'enum' in str(PROBLEMATIC_SCHEMA)}")
    print(f"  • 包含additionalProperties: {'additionalProperties' in str(PROBLEMATIC_SCHEMA)}")
    print(f"  • 嵌套深度: ~3层")
    
    print("\n🎯 可能的问题原因:")
    print("  1. anyOf + enum 组合可能导致正则表达式生成复杂度爆炸")
    print("  2. additionalProperties: false 增加了约束复杂度")
    print("  3. 嵌套的object结构需要复杂的状态机")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="复现vLLM JSON schema冻结问题")
    parser.add_argument("--url", default="http://0.0.0.0:7509", help="vLLM服务器URL")
    parser.add_argument("--curl", action="store_true", help="只显示curl命令")
    parser.add_argument("--analyze", action="store_true", help="分析schema结构")
    
    args = parser.parse_args()
    
    if args.analyze:
        analyze_schema()
    
    if args.curl:
        test_freeze_with_curl_command()
    else:
        test_with_python(args.url)
