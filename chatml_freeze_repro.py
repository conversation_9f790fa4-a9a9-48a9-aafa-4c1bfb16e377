#!/usr/bin/env python3
"""
使用原始ChatML格式复现vLLM冻结问题
完全匹配pydantic-ai生成的请求格式
"""

import requests
import json
import time

def create_chatml_payload():
    """创建完全匹配原始日志的ChatML格式payload"""
    
    # 从日志中提取的原始prompt
    chatml_prompt = '<|im_start|>system\nplease extract the user profile information from the following text. The output should be a JSON object with the keys "name", "dob" (date of birth), and "bio" (a short biography). If any information is not available, leave that key out of the JSON object.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within XML tags:\n\n{"type": "function", "function": {"name": "final_result", "description": "The final response which ends this conversation", "parameters": {"additionalProperties": false, "properties": {"name": {"type": "string"}, "dob": {"format": "date", "type": "string"}, "bio": {"type": "string"}}, "type": "object"}}}\n\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": , "arguments": }\n</tool_call><|im_end|>\n<|im_start|>user\nMy name is Ben, I was born on January 28th 1990, I like the chain the dog and the pyramid.<|im_end|>\n<|im_start|>assistant\n'
    
    # 从日志中提取的guided_json schema
    guided_json_schema = {
        "type": "array",
        "minItems": 1,
        "items": {
            "type": "object",
            "anyOf": [
                {
                    "properties": {
                        "name": {
                            "type": "string",
                            "enum": ["final_result"]
                        },
                        "parameters": {
                            "additionalProperties": False,
                            "properties": {
                                "name": {"type": "string"},
                                "dob": {"format": "date", "type": "string"},
                                "bio": {"type": "string"}
                            },
                            "type": "object"
                        }
                    },
                    "required": ["name", "parameters"]
                }
            ]
        }
    }
    
    # 构造完整的payload
    payload = {
        "model": "Qwen/Qwen2.5-VL-3B-Instruct",
        "prompt": chatml_prompt,
        "max_tokens": 16126,
        "temperature": 0.7,
        "top_p": 0.8,
        "top_k": 20,
        "presence_penalty": 0.0,
        "frequency_penalty": 0.0,
        "repetition_penalty": 1.05,
        "guided_json": guided_json_schema,
        "skip_special_tokens": True,
        "spaces_between_special_tokens": True
    }
    
    return payload

def create_openai_format_payload():
    """创建OpenAI API格式的payload"""
    
    # 从日志中提取的system message
    system_content = "please extract the user profile information from the following text. The output should be a JSON object with the keys \"name\", \"dob\" (date of birth), and \"bio\" (a short biography). If any information is not available, leave that key out of the JSON object.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within XML tags:\n\n{\"type\": \"function\", \"function\": {\"name\": \"final_result\", \"description\": \"The final response which ends this conversation\", \"parameters\": {\"additionalProperties\": false, \"properties\": {\"name\": {\"type\": \"string\"}, \"dob\": {\"format\": \"date\", \"type\": \"string\"}, \"bio\": {\"type\": \"string\"}}, \"type\": \"object\"}}}\n\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{\"name\": , \"arguments\": }\n</tool_call>"
    
    # 从日志中提取的guided_json schema
    guided_json_schema = {
        "type": "array",
        "minItems": 1,
        "items": {
            "type": "object",
            "anyOf": [
                {
                    "properties": {
                        "name": {
                            "type": "string",
                            "enum": ["final_result"]
                        },
                        "parameters": {
                            "additionalProperties": False,
                            "properties": {
                                "name": {"type": "string"},
                                "dob": {"format": "date", "type": "string"},
                                "bio": {"type": "string"}
                            },
                            "type": "object"
                        }
                    },
                    "required": ["name", "parameters"]
                }
            ]
        }
    }
    
    # 构造OpenAI API格式的payload
    payload = {
        "model": "Qwen/Qwen2.5-VL-3B-Instruct",
        "messages": [
            {
                "role": "system",
                "content": system_content
            },
            {
                "role": "user",
                "content": "My name is Ben, I was born on January 28th 1990, I like the chain the dog and the pyramid."
            }
        ],
        "max_tokens": 16126,
        "temperature": 0.7,
        "top_p": 0.8,
        "top_k": 20,
        "presence_penalty": 0.0,
        "frequency_penalty": 0.0,
        "repetition_penalty": 1.05,
        "guided_json": guided_json_schema
    }
    
    return payload

def test_vllm_freeze(url="http://0.0.0.0:7509", format_type="chatml"):
    """测试vLLM冻结问题"""
    
    print(f"🚨 vLLM冻结漏洞复现测试 (使用{format_type}格式)")
    print("=" * 50)
    
    if format_type == "chatml":
        payload = create_chatml_payload()
        endpoint = f"{url}/v1/completions"
    else:
        payload = create_openai_format_payload()
        endpoint = f"{url}/v1/chat/completions"
    
    print(f"📤 发送请求到: {endpoint}")
    print(f"📦 Payload大小: {len(json.dumps(payload))} 字节")
    
    start_time = time.time()
    
    try:
        print(f"\n⏰ 开始时间: {time.strftime('%H:%M:%S')}")
        
        response = requests.post(
            endpoint,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60  # 60秒超时
        )
        
        elapsed_time = time.time() - start_time
        print(f"⏱️  响应时间: {elapsed_time:.2f}秒")
        
        if response.status_code == 200:
            print("✅ 请求成功完成")
            result = response.json()
            print(f"📝 响应内容: {json.dumps(result, indent=2)[:200]}...")
        else:
            print(f"❌ 请求失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        elapsed_time = time.time() - start_time
        print(f"🚨 请求超时! 经过 {elapsed_time:.2f}秒")
        print("💀 这可能表明服务器已经冻结")
        
    except Exception as e:
        print(f"💥 错误: {e}")

def generate_curl_commands():
    """生成可以直接使用的curl命令"""
    
    chatml_payload = create_chatml_payload()
    openai_payload = create_openai_format_payload()
    
    chatml_curl = f"""curl -X POST http://0.0.0.0:7509/v1/completions \\
  -H "Content-Type: application/json" \\
  -d '{json.dumps(chatml_payload, separators=(",", ":"))}'"""
    
    openai_curl = f"""curl -X POST http://0.0.0.0:7509/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -d '{json.dumps(openai_payload, separators=(",", ":"))}'"""
    
    print("🔧 ChatML格式curl命令:")
    print("=" * 50)
    print(chatml_curl)
    print("\n🔧 OpenAI API格式curl命令:")
    print("=" * 50)
    print(openai_curl)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="复现vLLM冻结问题 (使用原始ChatML格式)")
    parser.add_argument("--url", default="http://0.0.0.0:7509", help="vLLM服务器URL")
    parser.add_argument("--format", choices=["chatml", "openai"], default="chatml", 
                       help="请求格式: chatml (原始格式) 或 openai (API格式)")
    parser.add_argument("--curl", action="store_true", help="只显示curl命令")
    
    args = parser.parse_args()
    
    if args.curl:
        generate_curl_commands()
    else:
        test_vllm_freeze(args.url, args.format)
