#!/usr/bin/env python3
"""
演示如何在JSON payload中正确发送不同类型的消息
包括system、user、assistant消息的格式
"""

import json
import requests

def example_basic_chat():
    """基本的聊天请求示例"""
    payload = {
        "model": "Qwen/Qwen2.5-VL-3B-Instruct",
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user", 
                "content": "Hello, how are you?"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    return payload

def example_multi_turn_chat():
    """多轮对话示例"""
    payload = {
        "model": "Qwen/Qwen2.5-VL-3B-Instruct",
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant that answers questions about programming."
            },
            {
                "role": "user",
                "content": "What is Python?"
            },
            {
                "role": "assistant", 
                "content": "Python is a high-level programming language known for its simplicity and readability."
            },
            {
                "role": "user",
                "content": "Can you give me an example of Python code?"
            }
        ],
        "max_tokens": 200,
        "temperature": 0.7
    }
    return payload

def example_with_guided_json():
    """带有guided_json的请求示例"""
    payload = {
        "model": "Qwen/Qwen2.5-VL-3B-Instruct",
        "messages": [
            {
                "role": "system",
                "content": "Extract user information and return it as JSON with keys: name, age, location."
            },
            {
                "role": "user",
                "content": "Hi, I'm Alice, I'm 25 years old and I live in New York."
            }
        ],
        "guided_json": {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer"},
                "location": {"type": "string"}
            },
            "required": ["name"]
        },
        "max_tokens": 100
    }
    return payload

def example_problematic_schema():
    """导致vLLM冻结的problematic schema示例"""
    payload = {
        "model": "Qwen/Qwen2.5-VL-3B-Instruct",
        "messages": [
            {
                "role": "system",
                "content": "please extract the user profile information from the following text. The output should be a JSON object with the keys \"name\", \"dob\" (date of birth), and \"bio\" (a short biography). If any information is not available, leave that key out of the JSON object."
            },
            {
                "role": "user",
                "content": "My name is Ben, I was born on January 28th 1990, I like the chain the dog and the pyramid."
            }
        ],
        "guided_json": {
            "type": "array",
            "minItems": 1,
            "items": {
                "type": "object",
                "anyOf": [
                    {
                        "properties": {
                            "name": {
                                "type": "string",
                                "enum": ["final_result"]
                            },
                            "parameters": {
                                "additionalProperties": False,
                                "properties": {
                                    "name": {"type": "string"},
                                    "dob": {"format": "date", "type": "string"},
                                    "bio": {"type": "string"}
                                },
                                "type": "object"
                            }
                        },
                        "required": ["name", "parameters"]
                    }
                ]
            }
        },
        "max_tokens": 100,
        "temperature": 0.7
    }
    return payload

def send_request(payload, url="http://0.0.0.0:7509"):
    """发送请求到vLLM服务器"""
    try:
        response = requests.post(
            f"{url}/v1/chat/completions",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            print(f"Error: HTTP {response.status_code}")
            print(response.text)
            return None
            
    except requests.exceptions.Timeout:
        print("Request timed out - server may be frozen")
        return None
    except Exception as e:
        print(f"Error: {e}")
        return None

def print_payload_example(name, payload):
    """打印payload示例"""
    print(f"\n{'='*50}")
    print(f"📋 {name}")
    print(f"{'='*50}")
    print(json.dumps(payload, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="JSON消息格式示例")
    parser.add_argument("--example", choices=["basic", "multi", "guided", "problematic"], 
                       default="basic", help="选择示例类型")
    parser.add_argument("--send", action="store_true", help="实际发送请求")
    parser.add_argument("--url", default="http://0.0.0.0:7509", help="vLLM服务器URL")
    
    args = parser.parse_args()
    
    examples = {
        "basic": ("基本聊天请求", example_basic_chat()),
        "multi": ("多轮对话请求", example_multi_turn_chat()),
        "guided": ("带guided_json的请求", example_with_guided_json()),
        "problematic": ("导致冻结的problematic schema", example_problematic_schema())
    }
    
    name, payload = examples[args.example]
    print_payload_example(name, payload)
    
    if args.send:
        print(f"\n🚀 发送请求到: {args.url}")
        result = send_request(payload, args.url)
        if result:
            print("\n✅ 响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print(f"\n💡 使用 --send 参数来实际发送这个请求")
        print(f"💡 使用 --example {args.example} --send 来发送请求")
