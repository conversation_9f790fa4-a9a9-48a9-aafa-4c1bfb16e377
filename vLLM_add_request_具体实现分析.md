# vLLM `_add_request` 方法具体实现分析

## 概述

本文档详细分析 `_add_request` 方法中两个关键调用的具体实现：
1. `self.output_processor.add_request()`
2. `await self.engine_core.add_request_async(request)`

## 1. `_add_request` 方法回顾

<augment_code_snippet path="vllm/v1/engine/async_llm.py" mode="EXCERPT">
````python
async def _add_request(self, request: EngineCoreRequest,
                       prompt: Optional[str],
                       parent_req: Optional[ParentRequest], index: int,
                       queue: RequestOutputCollector):

    # Add the request to OutputProcessor (this process).
    self.output_processor.add_request(request, prompt, parent_req, index, queue)

    # Add the EngineCoreRequest to EngineCore (separate process).
    await self.engine_core.add_request_async(request)

    if self.log_requests:
        logger.info("Added request %s.", request.request_id)
````
</augment_code_snippet>

## 2. 第一个调用：`self.output_processor.add_request()`

### 2.1 OutputProcessor.add_request() 实现

<augment_code_snippet path="vllm/v1/engine/output_processor.py" mode="EXCERPT">
````python
def add_request(
    self,
    request: EngineCoreRequest,
    prompt: Optional[str],
    parent_req: Optional[ParentRequest] = None,
    request_index: int = 0,
    queue: Optional[RequestOutputCollector] = None,
) -> None:
    request_id = request.request_id
    if request_id in self.request_states:
        raise ValueError(f"Request id {request_id} already running.")

    # 创建请求状态对象
    req_state = RequestState.from_new_request(
        tokenizer=self.tokenizer.get_lora_tokenizer(request.lora_request),
        request=request,
        prompt=prompt,
        parent_req=parent_req,
        request_index=request_index,
        queue=queue,
        log_stats=self.log_stats)
    
    # 存储请求状态
    self.request_states[request_id] = req_state
    self.lora_states.add_request(req_state)
    
    # 如果是子请求，记录父请求
    if parent_req:
        self.parent_requests[parent_req.request_id] = parent_req
````
</augment_code_snippet>

**功能说明**:
- 创建 `RequestState` 对象来跟踪请求状态
- 将请求状态存储在 `self.request_states` 字典中
- 处理 LoRA 相关状态
- 管理父子请求关系

### 2.2 RequestState 创建过程

`RequestState.from_new_request()` 会：
1. 初始化 tokenizer 和请求参数
2. 创建输出队列绑定
3. 设置统计日志记录
4. 准备解码状态跟踪

## 3. 第二个调用：`await self.engine_core.add_request_async(request)`

### 3.1 调用链路分析

```
AsyncLLM._add_request()
  ↓
self.engine_core.add_request_async()  # engine_core 是 AsyncMPClient 实例
  ↓
AsyncMPClient.add_request_async()
  ↓
await self._send_input(EngineCoreRequestType.ADD, request)
  ↓
self._send_input_message() 
  ↓
self.input_socket.send_multipart()  # ZMQ 消息发送
  ↓
[跨进程通信]
  ↓
EngineCore 进程接收消息
  ↓
EngineCore.add_request()
  ↓
self.scheduler.add_request()
```

### 3.2 AsyncMPClient.add_request_async() 实现

<augment_code_snippet path="vllm/v1/engine/core_client.py" mode="EXCERPT">
````python
async def add_request_async(self, request: EngineCoreRequest) -> None:
    request.client_index = self.client_index  # 设置客户端索引
    await self._send_input(EngineCoreRequestType.ADD, request)  # 发送请求
    self._ensure_output_queue_task()  # 确保输出队列任务运行
````
</augment_code_snippet>

### 3.3 _send_input() 方法实现

<augment_code_snippet path="vllm/v1/engine/core_client.py" mode="EXCERPT">
````python
def _send_input(self,
                request_type: EngineCoreRequestType,
                request: Any,
                engine: Optional[CoreEngine] = None) -> Awaitable[Any]:
    self.ensure_alive()  # 确保引擎进程存活
    if engine is None:
        engine = self.core_engine

    # 编码消息：(请求类型, 序列化的请求数据)
    message = (request_type.value, *self.encoder.encode(request))
    return self._send_input_message(message, engine, request)
````
</augment_code_snippet>

### 3.4 _send_input_message() 方法实现

<augment_code_snippet path="vllm/v1/engine/core_client.py" mode="EXCERPT">
````python
def _send_input_message(self, message: tuple[bytestr, ...], 
                        engine: CoreEngine, objects: Any) -> Awaitable[Any]:
    """
    objects 是一个引用，用于在 zmq 完成缓冲区处理之前保持对象存活，
    以防它们是从请求中的张量提取的。
    """
    self.ensure_alive()
    self.free_pending_messages()

    # 构造完整消息：(引擎标识, 请求类型, 序列化数据)
    msg = (engine.identity, ) + message
    
    if not objects or len(msg) <= 3:
        # 没有辅助缓冲区 => 请求中没有张量支持缓冲区
        return self.input_socket.send_multipart(msg, copy=False)

    # 有张量数据，需要跟踪消息发送状态
    future: asyncio.Future[zmq.MessageTracker]
    future = self.input_socket.send_multipart(msg, copy=False, track=True)

    def add_pending(f: asyncio.Future[zmq.MessageTracker]):
        with contextlib.suppress(BaseException):
            self.add_pending_message(f.result(), objects)

    future.add_done_callback(add_pending)
    return future
````
</augment_code_snippet>

**关键技术点**:
- 使用 ZMQ (ZeroMQ) 进行进程间通信
- 支持张量数据的零拷贝传输
- 异步消息发送和跟踪

## 4. 接收端：EngineCore.add_request() 实现

### 4.1 EngineCore 接收处理

<augment_code_snippet path="vllm/v1/engine/core.py" mode="EXCERPT">
````python
def add_request(self, request: EngineCoreRequest):
    """Add request to the scheduler."""

    # 处理多模态输入缓存
    if request.mm_hashes is not None:
        assert request.mm_inputs is not None
        request.mm_inputs = self.mm_input_cache_server.get_and_update_p1(
            request.mm_inputs, request.mm_hashes)

    # 转换为内部请求对象
    req = Request.from_engine_core_request(request)
    
    # 处理结构化输出（如 guided_regex）
    if req.use_structured_output:
        # 异步启动语法编译
        self.structured_output_manager.grammar_init(req)

    # 处理 KV 传输参数
    if req.kv_transfer_params is not None and (
            not self.scheduler.get_kv_connector()):
        logger.warning("Got kv_transfer_params, but no KVConnector found. "
                       "Disabling KVTransfer for this request.")

    # 将请求添加到调度器
    self.scheduler.add_request(req)
````
</augment_code_snippet>

**关键处理步骤**:
1. **多模态缓存**: 处理图像、视频等多模态输入的缓存
2. **请求转换**: 将 `EngineCoreRequest` 转换为内部 `Request` 对象
3. **结构化输出**: 如果请求包含 `guided_regex` 等，启动语法编译
4. **调度器添加**: 将请求添加到调度器队列等待处理

### 4.2 结构化输出处理（guided_regex 相关）

当请求包含 `guided_regex` 时：

```python
if req.use_structured_output:
    # 这里会触发我们之前分析的 guided_regex 编译过程
    self.structured_output_manager.grammar_init(req)
```

这就是我们之前分析的 guided_regex 污点传播路径中的关键节点。

## 5. 完整调用流程总结

```
HTTP Request
  ↓
AsyncLLM._add_request()
  ├─ OutputProcessor.add_request()     # 当前进程：状态管理
  │   ├─ 创建 RequestState
  │   ├─ 存储请求状态
  │   └─ 绑定输出队列
  │
  └─ engine_core.add_request_async()   # 跨进程：实际处理
      ├─ AsyncMPClient.add_request_async()
      ├─ _send_input() 编码消息
      ├─ _send_input_message() ZMQ发送
      ├─ [跨进程通信]
      ├─ EngineCore.add_request()
      ├─ 处理多模态缓存
      ├─ 结构化输出编译 (guided_regex)
      └─ scheduler.add_request() 调度
```

**架构特点**:
- **双重处理**: 当前进程管理状态，独立进程执行推理
- **异步通信**: 使用 ZMQ 实现高效的进程间异步通信
- **状态分离**: 输出处理和引擎执行分离，提高并发性能
- **资源隔离**: 引擎进程独立运行，避免阻塞 API 服务
