#!/usr/bin/env python3
"""
复现vLLM服务冻结的脚本
基于pydantic-ai发送的实际报文
"""

import requests
import json
import time

def create_problematic_payload():
    """创建导致vLLM冻结的具体payload"""
    
    # 这是从日志中提取的导致问题的guided_decoding JSON schema
    problematic_schema = {
        "type": "array",
        "minItems": 1,
        "items": {
            "type": "object",
            "anyOf": [
                {
                    "properties": {
                        "name": {
                            "type": "string",
                            "enum": ["final_result"]
                        },
                        "parameters": {
                            "additionalProperties": False,
                            "properties": {
                                "name": {"type": "string"},
                                "dob": {"format": "date", "type": "string"},
                                "bio": {"type": "string"}
                            },
                            "type": "object"
                        }
                    },
                    "required": ["name", "parameters"]
                }
            ]
        }
    }
    
    # 从日志中提取的完整prompt
    system_prompt = """please extract the user profile information from the following text. The output should be a JSON object with the keys "name", "dob" (date of birth), and "bio" (a short biography). If any information is not available, leave that key out of the JSON object.

# Tools

You may call one or more functions to assist with the user query.

You are provided with function signatures within XML tags:

{"type": "function", "function": {"name": "final_result", "description": "The final response which ends this conversation", "parameters": {"additionalProperties": false, "properties": {"name": {"type": "string"}, "dob": {"format": "date", "type": "string"}, "bio": {"type": "string"}}, "type": "object"}}}


For each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:
<tool_call>
{"name": , "arguments": }
</tool_call>"""

    user_message = "My name is Ben, I was born on January 28th 1990, I like the chain the dog and the pyramid."
    
    # 构造完整的OpenAI API请求
    payload = {
        "model": "qwen2.5-14b-instruct",
        "messages": [
            {
                "role": "system",
                "content": system_prompt
            },
            {
                "role": "user", 
                "content": user_message
            }
        ],
        "temperature": 0.7,
        "top_p": 0.8,
        "top_k": 20,
        "repetition_penalty": 1.05,
        "max_tokens": 16126,
        "stream": False,
        # 这是导致问题的关键参数
        "guided_json": problematic_schema
    }
    
    return payload

def test_vllm_freeze(base_url="http://0.0.0.0:7509"):
    """测试vLLM冻结问题"""
    
    print("🚨 vLLM冻结漏洞复现测试")
    print("=" * 50)
    
    payload = create_problematic_payload()
    
    print("📤 发送导致冻结的payload...")
    print(f"🎯 目标URL: {base_url}/v1/chat/completions")
    print(f"📦 Guided JSON Schema大小: {len(json.dumps(payload['guided_json']))} 字节")
    
    # 显示关键的schema结构
    print("\n🔍 问题Schema结构:")
    print(json.dumps(payload['guided_json'], indent=2))
    
    start_time = time.time()
    
    try:
        print(f"\n⏰ 开始时间: {time.strftime('%H:%M:%S')}")
        
        response = requests.post(
            f"{base_url}/v1/chat/completions",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60  # 60秒超时
        )
        
        elapsed_time = time.time() - start_time
        print(f"⏱️  响应时间: {elapsed_time:.2f}秒")
        
        if response.status_code == 200:
            print("✅ 请求成功完成")
            result = response.json()
            if 'choices' in result and result['choices']:
                print(f"📝 响应内容: {result['choices'][0]['message']['content'][:200]}...")
        else:
            print(f"❌ 请求失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        elapsed_time = time.time() - start_time
        print(f"🚨 请求超时! 经过 {elapsed_time:.2f}秒")
        print("💀 这可能表明服务器已经冻结")
        return True
        
    except requests.exceptions.ConnectionError as e:
        print(f"🔌 连接错误: {e}")
        print("💀 服务器可能已经崩溃或冻结")
        return True
        
    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"💥 其他错误 (经过 {elapsed_time:.2f}秒): {e}")
        return True
    
    return False

def test_simple_request(base_url="http://0.0.0.0:7509"):
    """测试简单请求以验证服务器状态"""
    
    print("\n🔍 测试服务器基本功能...")
    
    simple_payload = {
        "model": "qwen2.5-14b-instruct",
        "messages": [
            {"role": "user", "content": "Hello, how are you?"}
        ],
        "max_tokens": 50
    }
    
    try:
        response = requests.post(
            f"{base_url}/v1/chat/completions",
            json=simple_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 服务器基本功能正常")
            return True
        else:
            print(f"❌ 服务器响应异常: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 服务器无法响应: {e}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="复现vLLM冻结漏洞")
    parser.add_argument("--url", default="http://0.0.0.0:7509", 
                       help="vLLM服务器URL (默认: http://0.0.0.0:7509)")
    parser.add_argument("--test-simple", action="store_true",
                       help="先测试简单请求")
    
    args = parser.parse_args()
    
    if args.test_simple:
        if not test_simple_request(args.url):
            print("❌ 服务器基本功能测试失败，退出")
            exit(1)
    
    # 执行冻结测试
    freeze_detected = test_vllm_freeze(args.url)
    
    if freeze_detected:
        print("\n🚨 检测到服务器冻结或异常!")
        print("📋 建议检查:")
        print("  1. vLLM服务器日志")
        print("  2. 服务器CPU/内存使用情况") 
        print("  3. 尝试重启vLLM服务")
    else:
        print("\n✅ 测试完成，未检测到明显的冻结问题")
