from datetime import date
import asyncio
from typing import Dict, List, Optional
from loguru import logger
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic import ValidationError, BaseModel
from typing_extensions import TypedDict

class UserProfile(BaseModel):
    name: Optional[str] = None
    dob: Optional[date] = None
    bio: Optional[str] = None

class Chatbot:
    def __init__(self):
        self.model = OpenAIModel(
            model_name="qwen2.5-14b-instruct",
            provider=OpenAIProvider(
                base_url="http://0.0.0.0:7509/v1/",
                api_key="password",
                timeout=30  # 添加超时设置
            ),
        )
        self.agent = Agent(
            model=self.model,
            system_prompt='Extract user profile information and return a JSON object with keys "name", "dob" (date of birth), and "bio" (biography). If information is missing, omit that key.',
            result_type=UserProfile
        )

async def run_with_timeout(coro, timeout=60):
    """运行协程，添加超时机制"""
    try:
        return await asyncio.wait_for(coro, timeout=timeout)
    except asyncio.TimeoutError:
        print(f"Operation timed out after {timeout} seconds")
        return None

async def main():
    chatbot = Chatbot()
    user_input = 'My name is Ben, I was born on January 28th 1990, I like the chain the dog and the pyramid.'

    try:
        # 使用超时机制运行
        result = await run_with_timeout(chatbot.agent.run(user_input), timeout=30)
        if result:
            print("Result:", result.data)
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
