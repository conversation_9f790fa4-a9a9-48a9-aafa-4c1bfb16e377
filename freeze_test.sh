#!/bin/bash

# vLLM冻结问题复现脚本
# 基于pydantic-ai发送的实际problematic payload

echo "🚨 vLLM JSON Schema冻结漏洞复现测试"
echo "=================================================="

# 设置目标URL
VLLM_URL="${1:-http://0.0.0.0:7509}"
echo "🎯 目标服务器: $VLLM_URL"

# 从日志中提取的导致冻结的确切JSON schema
# 这个schema是pydantic-ai生成的，包含复杂的anyOf + enum结构
read -r -d '' PROBLEMATIC_PAYLOAD << 'EOF'
{
  "model": "Qwen/Qwen2.5-VL-3B-Instruct",
  "messages": [
    {
      "role": "system",
      "content": "please extract the user profile information from the following text. The output should be a JSON object with the keys \"name\", \"dob\" (date of birth), and \"bio\" (a short biography). If any information is not available, leave that key out of the JSON object.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within XML tags:\n\n{\"type\": \"function\", \"function\": {\"name\": \"final_result\", \"description\": \"The final response which ends this conversation\", \"parameters\": {\"additionalProperties\": false, \"properties\": {\"name\": {\"type\": \"string\"}, \"dob\": {\"format\": \"date\", \"type\": \"string\"}, \"bio\": {\"type\": \"string\"}}, \"type\": \"object\"}}}\n\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{\"name\": , \"arguments\": }\n</tool_call>"
    },
    {
      "role": "user",
      "content": "My name is Ben, I was born on January 28th 1990, I like the chain the dog and the pyramid."
    }
  ],
  "guided_json": {
    "type": "array",
    "minItems": 1,
    "items": {
      "type": "object",
      "anyOf": [
        {
          "properties": {
            "name": {
              "type": "string",
              "enum": ["final_result"]
            },
            "parameters": {
              "additionalProperties": false,
              "properties": {
                "name": {"type": "string"},
                "dob": {"format": "date", "type": "string"},
                "bio": {"type": "string"}
              },
              "type": "object"
            }
          },
          "required": ["name", "parameters"]
        }
      ]
    }
  },
  "max_tokens": 100,
  "temperature": 0.7
}
EOF

echo "📦 发送problematic payload..."
echo "⏰ 开始时间: $(date '+%H:%M:%S')"

# 记录开始时间
START_TIME=$(date +%s)

# 发送请求，设置60秒超时
echo "🚀 执行请求..."
RESPONSE=$(curl -s -w "\n%{http_code}\n%{time_total}" \
  -X POST "$VLLM_URL/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d "$PROBLEMATIC_PAYLOAD" \
  --max-time 60 \
  --connect-timeout 10 2>&1)

# 计算执行时间
END_TIME=$(date +%s)
ELAPSED=$((END_TIME - START_TIME))

echo "⏱️  总执行时间: ${ELAPSED}秒"
echo "⏰ 结束时间: $(date '+%H:%M:%S')"

# 分析响应
if [ $? -eq 28 ]; then
    echo ""
    echo "🚨 请求超时!"
    echo "💀 这很可能表明vLLM服务器已经冻结"
    echo "📋 建议检查:"
    echo "   • vLLM服务器日志"
    echo "   • 服务器CPU/内存使用情况"
    echo "   • 考虑重启vLLM服务"
    exit 1
elif [ $? -eq 7 ]; then
    echo ""
    echo "🔌 连接失败!"
    echo "💀 服务器可能已经崩溃或无响应"
    exit 1
else
    # 提取HTTP状态码和响应时间
    HTTP_CODE=$(echo "$RESPONSE" | tail -n 2 | head -n 1)
    TIME_TOTAL=$(echo "$RESPONSE" | tail -n 1)
    RESPONSE_BODY=$(echo "$RESPONSE" | head -n -2)
    
    echo ""
    echo "📊 响应结果:"
    echo "   HTTP状态码: $HTTP_CODE"
    echo "   响应时间: ${TIME_TOTAL}秒"
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ 请求成功完成"
        echo "📝 响应内容预览:"
        echo "$RESPONSE_BODY" | head -c 200
        echo "..."
    else
        echo "❌ 请求失败"
        echo "错误响应: $RESPONSE_BODY"
    fi
fi

echo ""
echo "🔍 问题分析:"
echo "导致冻结的JSON schema特征:"
echo "  • type: array + minItems"
echo "  • 复杂的anyOf结构"
echo "  • enum约束 + additionalProperties: false"
echo "  • 嵌套的object定义"
echo ""
echo "💡 这种schema可能导致outlines_core.fsm.json_schema.build_regex_from_schema"
echo "   函数进入计算密集型循环，消耗大量CPU资源并阻塞服务"
